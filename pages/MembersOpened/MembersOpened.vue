<template>
  <view class="page">
    <image style="width: 100vw;height: 100vh;" mode="scaleToFill" :src="QRCode" @click="seeImg"></image>
  </view>
</template>

<script>
import UniShare from '@/uni_modules/uni-share/js_sdk/uni-share.js';

const uniShare = new UniShare();

export default {
  data() {
    return {
      QRCode: getApp().globalData.config.myQRCode,
      shareUrl: '',
    };
  },
  onBackPress({from}) {
    if(from === 'backbutton'){
      this.$nextTick(() => {
        uniShare.hide()
      })
      return uniShare.isShow;
    }
  },
  onLoad() {
    this.$login.checkLogin({login: true})
    this.makeQRCode()
  },
  methods: {
    seeImg() {
      // 判断是否在App环境
      if (uni.getSystemInfoSync().uniPlatform === '等app备案') {
        // 使用uni-share插件进行分享
        uniShare.show({
          content: {
            type: 0,
            href: this.shareUrl || getApp().globalData.config.shareUrl || '',
            title: '我的二维码',
            summary: '扫描二维码加入我们',
            imageUrl: this.QRCode
          },
          menus: [
            {
              "img": "/static/app-plus/sharemenu/wechatfriend.png",
              "text": "微信好友",
              "share": {
                "provider": "weixin",
                "scene": "WXSceneSession"
              }
            },
            {
              "img": "/static/app-plus/sharemenu/wechatmoments.png",
              "text": "微信朋友圈",
              "share": {
                "provider": "weixin",
                "scene": "WXSceneTimeline"
              }
            },
            {
              "img": "/static/app-plus/sharemenu/more.png",
              "text": "更多",
              "share": "shareSystem"
            }
          ],
          cancelText: "取消分享",
        }, e => {
          console.log(e);
          // 如果分享失败，回退到图片预览
          if (e.errMsg && e.errMsg.indexOf('fail') !== -1) {
            this.previewImage();
          }
        });
      } else {
        // 非App环境，直接预览图片
        this.previewImage();
      }
    },

    // 图片预览方法
    previewImage() {
      uni.previewImage({
        urls: [this.QRCode] // 需要预览的图片http链接列表
      });
    },
    makeQRCode() {
      uni.showLoading({
        title: "生成中",
        success: () => {
          this.$http.get('makeQRCode', {
            token: getApp().globalData.token
          }).then(res => {
            setTimeout(function () {
              uni.hideLoading()
            }, 500)
            if (res.code === 0) {
              this.QRCode = res.data.myQRcode
              this.shareUrl = res.data.shareUrl || getApp().globalData.config.shareUrl
            } else {
              uni.showToast({
                title: res.msg,
                icon: 'none'
              })
            }
          })
        }
      })
    },
  }
}
</script>
