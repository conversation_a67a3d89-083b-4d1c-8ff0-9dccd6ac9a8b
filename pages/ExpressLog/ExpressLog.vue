<template>
  <view class="page">
    <!-- 物流信息头部 -->
    <view class="express-header">

      <view class="goods-info">
        <view class="goods-thumb">
          <image :src="api + expressInfo.goodsImg" mode="aspectFill"></image>
        </view>
        <view class="goods-title">
          <text class="goods-name">{{ expressInfo.goodsTitle || '' }}</text>
          <view class="express-info" v-if="expressInfo.express_info">
            <text class="company-name">
              {{ (expressInfo.express_info.logisticsCompanyName || '未知物流') + ' ：' + expressInfo.express_info.mailNo }}
            </text>
            <text class="copy-btn" @click="copyExpressNo(expressInfo.express_info.mailNo)">复制</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 物流状态 -->
    <view class="express-status" v-if="expressInfo.express_info">
      <view class="status-info">
        <text class="status-text">{{ expressInfo.express_info.logisticsStatusDesc }}</text>
        <text class="status-time">{{ expressInfo.express_info.theLastTime }}</text>
      </view>
      <view class="status-message">
        <text>{{ expressInfo.express_info.theLastMessage }}</text>
      </view>
    </view>

    <!-- 物流状态空状态 -->
    <view class="express-empty" v-else>
      <view class="empty-icon">
        <text class="iconfont icon-box"></text>
      </view>
      <view class="empty-text">
        <text class="empty-title">暂无物流信息</text>
        <text class="empty-desc">商品可能还未发货，请耐心等待</text>
      </view>
      <view class="empty-actions" v-if="false">
        <text class="refresh-btn" @click="getExpressInfo">刷新</text>
        <text class="contact-btn" @click="contactService">联系客服</text>
      </view>
    </view>

    <!-- 物流轨迹 -->
    <view class="express-timeline" v-if="expressInfo.express_info && expressInfo.express_info.logisticsTraceDetailList && expressInfo.express_info.logisticsTraceDetailList.length > 0">
      <view class="timeline-title">
        <text>物流信息</text>
      </view>
      <view class="timeline-list" >
        <view class="timeline-item" v-for="(item, index) in expressInfo.express_info.logisticsTraceDetailList"
              :key="index" :class="{active: index === 0}">
          <view class="timeline-dot"></view>
          <view class="timeline-content">
            <view class="timeline-time">
              <text>{{ item.timeDesc }}</text>
            </view>
            <view class="timeline-desc">
              <text>{{ item.desc }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 物流轨迹空状态 -->
    <view class="timeline-empty" v-else-if="expressInfo.express_info && (!expressInfo.express_info.logisticsTraceDetailList || expressInfo.express_info.logisticsTraceDetailList.length === 0)">
      <view class="timeline-title">
        <text>物流信息</text>
      </view>
      <view class="empty-timeline">
        <view class="empty-icon">
          <text class="iconfont icon-box"></text>
        </view>
        <view class="empty-text">
          <text class="empty-title">暂无物流轨迹</text>
          <text class="empty-desc">物流信息正在更新中，请稍后查看</text>
        </view>
      </view>
    </view>

    <!-- 底部联系信息 -->
    <view class="express-contact" v-if="expressInfo.express_info.cpMobile">
      <view class="contact-info">
        <text>快递员：{{ expressInfo.express_info.cpMobile }}</text>
        <text class="contact-btn" @click="callExpress(expressInfo.express_info.cpMobile)">联系快递员</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      api: getApp().globalData.apiUrl,
      orderID: 0,
      expressNo: '',
      expressInfo: {
        goodsTitle: '',
        goodsImg: '',
        express_info: {
          logisticsCompanyName: '',
          mailNo: '',
          logisticsStatusDesc: '',
          theLastTime: '',
          theLastMessage: '',
          logisticsTraceDetailList: [],
          cpMobile: ''
        }
      }
    };
  },
  onLoad(params) {
    this.$login.checkLogin({login: true})
    this.orderID = params.orderID
    this.expressNo = params.expressNo
    this.getExpressInfo()
  },
  methods: {
    /**
     * 复制物流单号
     */
    copyExpressNo(expressNo) {
      uni.setClipboardData({
        data: expressNo,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'none'
          })
        }
      })
    },

    /**
     * 获取物流信息
     */
    getExpressInfo() {
      uni.showLoading({
        title: '加载中...'
      })
      this.$http.get('getExpressInfo', {
        token: getApp().globalData.token,
        orderID: this.orderID,
        expressNo: this.expressNo,
        myOrder: 1
      }).then(res => {
        uni.hideLoading()
        if (res.code == 0) {
          this.expressInfo = res.data
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 1500)
        }
      }).catch(err => {
        uni.hideLoading()
        uni.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none'
        })
        console.error('获取物流信息失败:', err)
      })
    },

    /**
     * 联系快递员
     * @param {String} mobile 快递员电话
     */
    callExpress(mobile) {
      if (!mobile) return
      uni.makePhoneCall({
        phoneNumber: mobile
      })
    },

    /**
     * 联系客服
     */
    contactService() {
      // 获取全局配置中的客服电话
      const servicePhone = getApp().globalData.config?.mobile || getApp().globalData.config?.servicePhone;

      if (servicePhone) {
        uni.makePhoneCall({
          phoneNumber: servicePhone,
          fail: () => {
            uni.showToast({
              title: '拨打电话失败',
              icon: 'none'
            });
          }
        });
      } else {
        uni.showToast({
          title: '客服电话暂不可用',
          icon: 'none'
        });
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import 'ExpressLog.scss';
</style>
