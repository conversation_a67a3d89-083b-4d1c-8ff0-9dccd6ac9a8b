<template>
	<view class="page">
		<!-- 地址列表 -->
		<view class="address-list">
			<view class="list" v-for="(item,index) in addressList" :key="index" @click="selectMode ? selectID(item.id) : null">
				<view class="name-phone">
					<view class="name">
						<text class="one-omit">{{item.username}}</text>
					</view>
					<view class="phone">
						<text>{{item.mobile}}</text>
						<text class="tag" v-if="item.default==1">默认</text>
						<text class="tag blue" v-if="item.tag">{{item.tag}}</text>
					</view>
				</view>
				<view class="address-edit">
					<view class="address">
						<text>{{item.address1}}{{item.address2}}</text>
					</view>
					<view class="edit">
						<text class="iconfont icon-edit1" @click.stop="onAddressEdit(1,item.id)"></text>
						<text class="iconfont icon-laji" @click.stop="deleteAddress(item.id)"></text>
<!--						<text class="iconfont icon-duihao" v-if="selectMode" @click.stop="selectID(item.id)"></text>-->
					</view>
				</view>
			</view>
			<view v-if="Object.keys(addressList).length === 0" class="on-anything">
				<view class="empty-card">暂无收货地址</view>
			</view>
		</view>
		<!-- 添加地址 -->
		<view class="add-address">
			<view class="btn" @click="onAddressEdit(2)">
				<text>新建收货地址</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				addressList:{},
				selectMode: false // 是否为选择地址模式
			};
		},
		onLoad(options) {
			this.$login.checkLogin({login:true})
			// 判断是否为选择地址模式
			if (options.select && options.select == '1') {
				this.selectMode = true
				uni.setNavigationBarTitle({
					title: '选择收货地址'
				})
			}
		},
		onShow() {
			this.getList()
		},
		methods:{
			selectID(cid){
				if(cid>0){
					for(var i in this.addressList){
						var d = this.addressList[i]
						if(d.id == cid){
							getApp().globalData.selectAddress=d
							uni.showToast({
								title: '已选择该地址',
								icon: 'success',
								duration: 1000
							})
							setTimeout(() => {
								uni.navigateBack()
							}, 1000)
							break
						}
					}
				}
			},
			getList(){
				var that = this
				this.$http.get('getAddressList', {
					token: getApp().globalData.token
				}).then(res => {
					uni.stopPullDownRefresh();
					if (res.code == 0) {
						this.addressList = res.data
					} else {
						uni.showToast({
							title:res.msg
						})
						this.status = 'noMore'
					}
				})
			},
			/**
			 * 编辑地址点击
			 */
			onAddressEdit(type,cid){
				uni.navigateTo({
					url: '/pages/AddressEdit/AddressEdit?type=' + type+"&id="+cid,
				})
			},
			/**
			 * 删除地址
			 */
			deleteAddress(id) {
				uni.showModal({
					title: '提示',
					content: '确定要删除该地址吗？',
					success: (res) => {
						if (res.confirm) {
							this.$http.post('delAddress', {
								token: getApp().globalData.token,
								id: id
							}).then(res => {
								if (res.code == 0) {
									uni.showToast({
										title: '删除成功',
										icon: 'success'
									});
									// 重新获取地址列表
									this.getList();
								} else {
									uni.showToast({
										title: res.msg || '删除失败',
										icon: 'none'
									});
								}
							}).catch(err => {
								uni.showToast({
									title: '网络异常，请稍后重试',
									icon: 'none'
								});
							});
						}
					}
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'AddressList.scss';
</style>
