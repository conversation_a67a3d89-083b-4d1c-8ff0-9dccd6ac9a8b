.page{
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: #f5f5f5;
}

/* 地址列表 */
.address-list{
	width: 100%;
	background-color: #f5f5f5;
	padding: 20rpx;
	padding-bottom: 120rpx;
	.list{
		display: flex;
		flex-direction: column;
		padding: 24rpx 30rpx;
		margin-bottom: 20rpx;
		background-color: #FFFFFF;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease;
		
		&:active {
			background-color: #f9f9f9;
			transform: scale(0.99);
		}
		.name-phone{
			display: flex;
			align-items: center;
			width: 100%;
			height: 80rpx;
			.name{
				display: flex;
				align-items: center;
				width: 30%;
				height: 100%;
				text{
					width: 100%;
					font-size: 30rpx;
					font-weight: normal;
					color: #333;
				}
			}
			.phone{
				display: flex;
				align-items: center;
				width: 70%;
				height: 100%;
				text{
					font-size: 30rpx;
					font-weight: normal;
					color: #333;
				}
				.tag{
					display: inline-block;
					font-size: 24rpx;
					padding: 4rpx 12rpx;
					margin-left: 20rpx;
					border-radius: 30rpx;
					background-color: #4CAF50;
					color: white;
				}
				.blue{
					background-color: #0099FF;
				}
			}
		}
		.address-edit{
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 100%;
			margin-top: 6rpx;
			.address{
				display: flex;
				align-items: center;
				width: 80%;
				text{
					font-size: 26rpx;
					color: #999;
				}
			}
			.edit{
				display: flex;
				align-items: center;
				justify-content: flex-end;
				gap: 20rpx;
				width: 20%;
				.iconfont {
					font-size: 38rpx;
					color: #555555;
				}
				.icon-edit1 {
					color: #0099FF;
				}
				.icon-laji {
					color: #FF5722;
				}
				.icon-duihao {
					color: #4CAF50;
					font-size: 42rpx;
				}
			}
		}
	}
}

/* 空地址提示 */
.on-anything {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
  
  .empty-card {
    background-color: #fff;
    padding: 40rpx;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    text-align: center;
    width: 80%;
  }
}

/* 添加地址 */
.add-address{
	position: fixed;
	left: 0;
	bottom: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100rpx;
	.btn{
		display: flex;
		align-items: center;
		justify-content: center;
		width: 80%;
		height: 70rpx;
		background: linear-gradient(to right,$base,$change-clor);
		border-radius: 70rpx;
		box-shadow: 0 10rpx 10rpx rgba(0, 0, 0, 0.1);
		text{
			font-size: 28rpx;
			color: #FFFFFF;
		}
	}
}