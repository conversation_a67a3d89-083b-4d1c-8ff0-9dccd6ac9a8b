<template>
	<view class="page">
<!-- 		<view class="head-back">
			<view class="back" @click="onBack">
				<text></text>
			</view>
			<view class="title">
				<text>我的订单</text>
			</view>
			<view class="more-icon">
				<view class="icon-list">
					<text class="iconfont icon-fadajing"></text>
				</view>
			</view>
		</view> -->
		<!-- 订单tab -->
		<view class="order-tab" style="display: flex;">
			<view class="tab" :class="{'action':OrderType==0}" @click="onOrderTab(0)">
				<text>全部</text>
				<text class="line"></text>
			</view>
<!-- 			<view class="tab" :class="{'action':OrderType==1}" @click="onOrderTab(1)">
				<text>待付款</text>
				<text class="line"></text>
			</view> -->
			<view class="tab" :class="{'action':OrderType==2}" @click="onOrderTab(2)">
				<text>待发货</text>
				<text class="line"></text>
			</view>
			<view class="tab" :class="{'action':OrderType==3}" @click="onOrderTab(3)">
				<text>待收货</text>
				<text class="line"></text>
			</view>
			<view class="tab" :class="{'action':OrderType==4}" @click="onOrderTab(4)">
				<text>已完成</text>
				<text class="line"></text>
			</view>
		</view>
		<!-- 订单列表 -->
		<view class="order-list">
			<view v-if="ordersList.length<1" class="on-anything">
				———— 没有任何数据 ————
			</view>
			<view class="list" v-for="(item,index) in ordersList" @click="onOrderList(item.id)" :key="index">
				<view class="title-status">
					<view class="title">
						<text>下单时间：{{item.addTime}}</text>
					</view>
					<view class="status">
						<text>{{item.statusStr}}</text>
						<text class="iconfont icon-laji del"></text>
					</view>
				</view>
				<view class="goods-list">
					<view class="goods">
						<view class="thumb">
							<image :src="api + item.goodsImg" mode=""></image>
						</view>
						<view class="item">
							<view class="goods-name">
								<text class="two-omit">{{item.goodsTitle}}</text>
								<text class="two-omit">数量：{{item.number}}</text>
							</view>
							<view class="goods-price">
								<text class="min">￥</text>
								<text class="max">{{item.amount}}</text>
								<!-- <text class="min">.00</text> -->
							</view>
						</view>
					</view>
				</view>
				<view class="status-btn">
					<block v-if="item.status==0">
					<view class="btn" @click.stop="delOrders(item.id)">
						<text>取消订单</text>
					</view>
					<view class="btn action" @click.stop="pay(item.id)">
						<text>立即付款</text>
					</view>
					</block>
					<view class="btn action" v-else>
						<text>订单详情</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				OrderType: 0,
				ordersList:{},
				api:getApp().globalData.apiUrl
			};
		},
		onLoad(params) {
			this.$login.checkLogin({login:true})
			this.OrderType = params.type;
			this.getOrderList()
		},
		onShow() {
			if(this.OrderType==4){
				// this.onOrderTab(0)
			}
		},
		methods:{
			delOrders($cid){
				var that = this
				uni.showModal({
					title:"是否确定删除?",
					success:function(res){
						if(res.confirm){
							that.$http.get('orderDel', {
								token: getApp().globalData.token,
								orderID:$cid
							}).then(res => {
								uni.showToast({
									title:res.msg
								})
								if (res.code == 0) {
									setTimeout(function(){
										that.getOrderList()
									},1500)
								}
							})
						}
					}
				})
			},
			pay(orderID){
				uni.redirectTo({
					url: '/pages/CashierDesk/CashierDesk?orderID='+orderID,
				})
			},
			getOrderList(){
				var that = this
				uni.showLoading({
					title:"加载中..."
				})
				this.$http.get('getOrderList', {
					token: getApp().globalData.token,
					OrderType:that.OrderType
				}).then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						that.ordersList = res.data
					} else {
						uni.showToast({
							title:res.msg
						})
					}
				})
			},
			/**
			 * 返回点击
			 */
			onBack(){
				uni.navigateBack();
			},
			/**
			 * 订单tab点击
			 */
			onOrderTab(type){
				this.OrderType = type;
        this.getOrderList()
			},
			/**
			 * 订单列表点击
			 */
			onOrderList(ordersID){
				uni.navigateTo({
					url: '/pages/OrderDetails/OrderDetails?ordersID='+ordersID,
				})
			},
      /**
       * 评价点击
       */
      onEvaluate(){
		uni.navigateTo({
			url: '/pages/MyEvaluatePush/MyEvaluatePush'
        })
      }
		}
	}
</script>

<style scoped lang="scss">
	@import 'MyOrderList.scss';
</style>
