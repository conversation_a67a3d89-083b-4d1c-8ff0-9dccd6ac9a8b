<template>
  <view class="page">
    <!-- 头部背景 -->
    <view class="head-bg">
      <view class="wallet-balance">
        <view class="wallet">
          <text>
            <text class="iconfont icon-user" style="margin-right: 20rpx;"></text>
            团队人数({{ total }})
          </text>
        </view>
      </view>

      <view class="bg">
        <image src="/static/integral_bg1.png" mode=""></image>
      </view>
    </view>
    <scroll-view class="record-list" @scrolltolower="lower" style="height: calc(100vh - 180rpx); box-sizing: border-box;" scroll-y>
      <view class="list" v-for="(item,index) in amountList" :key="index" v-if="amountList && amountList.length > 0">
        <view class="user-info">
          <view class="phone-number">用户名：{{ item.username || item.nickname }}
            <text class="status-tag" :class="{'status-pending': !item.hasAuction}">{{ item.hasAuction ? '已参拍' : '未参拍' }}</text>
          </view>
          <view class="register-date">注册时间：{{ item.addtime }}</view>
        </view>
        <view class="action-btn">
          <text @click="call(item.mobile)" class="call-btn">
            <text class="iconfont icon-dianhua" style="margin-right: 10rpx;"></text>一键拨号
          </text>
        </view>
      </view>
      <view v-if="amountList.length === 0 && !isLoading" class="on-anything">
        <view class="empty-card">暂无团队成员数据</view>
      </view>
      <view v-show="noMore && amountList.length > 0" class="on-anything">
        <view class="end-text">———— 没有更多了 ————</view>
      </view>
      <view v-if="isLoading" class="loading">
        <view class="loading-icon"></view>
        <text>加载中...</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      page: 1,
      amountList: [],
      amount: 0,
      cint: 0, //0所有日志，1审核中，2已提现，3订单日志
      goto: true,
      noMore: false,
      selectIndex: 0,
      isLoading: false,
      limit: 10,
      total: 0
    };
  },
  onLoad(params) {
    this.amount = params.amount || 0
    this.cint = params.cint || 0
    this.$login.checkLogin({
      login: true
    })
    this.selectIndex = params.cid ? params.cid : 0
    this.getUserAmount(this.selectIndex)
  },

  // 支持下拉刷新
  onPullDownRefresh() {
    this.getUserAmount(this.selectIndex)
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 1000)
  },
  methods: {
    call($mobile = '') {
      // #ifdef H5
      // H5 环境
      window.location.href = 'tel:' + $mobile;
      // #endif

      // #ifdef APP-PLUS
      // App 环境
      uni.makePhoneCall({
        phoneNumber: $mobile,
        success: function () {
          console.log('success');
        },
        fail: function () {
          console.log('fail');
        }
      });
      // #endif
    },
    getUserAmount(cint) {
      this.selectIndex = cint
      this.page = 1
      this.amountList = []
      this.noMore = false
      this.getUserAmountList()
    },
    getUserAmountList() {
      if (this.isLoading || this.noMore) return

      this.isLoading = true
      var that = this
      that.$http.post('getMyTeam', {
        token: getApp().globalData.token,
        page: that.page,
        limit: that.limit // 每页N条数据，便于分页加载
      }).then(res => {
        that.isLoading = false
        that.goto = true // 重置goto状态，允许再次触发加载

        if (res.code == 0) {
          this.total = res.count
          if (that.page === 1) {
            that.amountList = res.data || []
          } else {
            that.amountList = [...that.amountList, ...(res.data || [])]
          }

          // 如果返回的数据少于请求的数量，说明没有更多数据了
          that.noMore = !res.data || res.data.length < that.limit
          that.amount = res.msg
        } else {
          that.noMore = true
          uni.showToast({
            title: res.msg || '加载失败',
            icon: 'none'
          })
        }
      }).catch(err => {
        that.isLoading = false
        that.goto = true
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        })
      })
    },
    lower() {
      if (!this.noMore && !this.isLoading) {
        this.page++
        this.getUserAmountList()
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import 'Team.scss';
@import url('https://at.alicdn.com/t/c/font_4963421_7qle4khyd4c.css');
</style>
