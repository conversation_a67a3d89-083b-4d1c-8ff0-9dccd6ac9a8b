.page{
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: #FFFFFF;
}
.amount_classList{
	width: 100vw;padding: 0rpx 3vw;font-weight: bold;
}
.amount_classList view{
	padding: 20rpx 0;
	color: #fff;
}
.selectClass{border-bottom: 2px solid #fff;}
.head-bg{
	position: relative;
	width: 100%;
	height: 180rpx;
	background: linear-gradient($base,$change-clor);
	.head-user{
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 4%;
		height: 100rpx;
		.user{
			display: flex;
			align-items: center;
			image{
				width: 70rpx;
				height: 70rpx;
				border-radius: 100%;
			}
			text{
				font-size: 28rpx;
				color: #FFFFFF;
				margin-left: 20rpx;
			}
		}
		.exchange{
			display: flex;
			align-items: center;
			text{
				font-size: 28rpx;
				color: #FFFFFF;
			}
		}
	}
	.wallet-balance{
		display: flex;
		align-items: center;
		justify-content: center;
		width: 100%;
		height: 150rpx;
		.wallet{
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			text{
				font-size: 30rpx;
				color: #FFFFFF;
			}
			.number{
				font-size: 60rpx;
				margin-top: 10rpx;
				// font-weight: bold;
			}
		}
	}
	.bg{
		position: absolute;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 40rpx;
		image{
			width: 100%;
			height: 100%;
		}
	}
}

/* 加载动画和提示信息 */
.on-anything {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
  
  .empty-card {
    background-color: #fff;
    padding: 40rpx;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    text-align: center;
    width: 80%;
  }
  
  .end-text {
    padding: 20rpx 0;
  }
}

.loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  
  text {
    font-size: 24rpx;
    color: #999;
    margin-top: 10rpx;
  }
  
  .loading-icon {
    width: 40rpx;
    height: 40rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid $base;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 记录列表 */
.record-list{
	width: 100%;
	background-color: #f5f5f5;
	padding: 20rpx;
	.list{
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 30rpx;
		margin-bottom: 20rpx;
		background-color: #FFFFFF;
		border-radius: 16rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		.user-info {
			flex: 1;
			
			.phone-number {
				font-size: 30rpx;
				color: #333;
				margin-bottom: 10rpx;
				font-weight: normal;
				display: flex;
				align-items: center;
			}
			
			.status-tag {
				display: inline-block;
				font-size: 24rpx;
				padding: 4rpx 12rpx;
				margin-left: 20rpx;
				border-radius: 30rpx;
				background-color: #4CAF50;
				color: white;
			}
			
			.status-pending {
				background-color: #FF5722;
			}
			
			.register-date {
				font-size: 26rpx;
				color: #999;
				margin-top: 6rpx;
			}
		}
		
		.action-btn {
			.call-btn {
				display: inline-flex;
				align-items: center;
				background-color: rgba(255, 0, 0, 0.1);
				padding: 8rpx 24rpx;
				border-radius: 40rpx;
				color: #fe3b0f;
				font-size: 26rpx;
				box-shadow: 0 2rpx 6rpx rgba(254, 59, 15, 0.1);
				
				.iconfont {
					font-size: 28rpx;
				}
			}
		}
	}
}