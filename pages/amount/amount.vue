<template>
  <view class="page">
    <view class="s-flex s-flex-bt amount_classList">
      <view :class="selectIndex == 0?'selectClass':''" @click="getUserAmount(0)">拍豆兑换</view>
      <view :class="selectIndex == 1?'selectClass':''" @click="getUserAmount(1)">拍卖增值</view>
      <view :class="selectIndex == 2?'selectClass':''" @click="getUserAmount(2)">参拍货款</view>
      <!-- <view :class="selectIndex == 3?'selectClass':''">结算货款</view> -->
      <!-- <view :class="selectIndex == 4?'selectClass':''">助力金</view> -->
    </view>
    <view class="feedback-type list-input">
      <view class="title">
        <text>可用余额</text>
      </view>
      <view class="content">
        {{ amount }}
      </view>
    </view>
    <view class="feedback-type list-input">
      <view class="title">
        <text>目标账户</text>
      </view>
      <view class="content">
        <radio-group @change="selectTo" :key="_radioReset">
          <radio icon-color="#fff" value="1" v-if="selectIndex==0 || selectIndex==1" color="#fe3b0f"
                 style="font-size: 32rpx;transform: scale(0.8);">参拍货款
          </radio>
          <radio value="2" v-if="selectIndex==2 || selectIndex==1" color="#fe3b0f"
                 style="font-size: 32rpx;transform: scale(0.8);">提现
          </radio>
          <radio value="3" v-if="selectIndex==0" color="#fe3b0f" style="font-size: 32rpx;transform: scale(0.8);">
            其他用户
          </radio>
        </radio-group>
      </view>
    </view>
    <view class="feedback-type list-input" @click="inputNumber">
      <view class="title">
        <text>操作数量</text>
      </view>
      <view class="content s-flex">
        {{ numbers }}
        <view class="more" @click="numbers = amount" style="margin-left: 20rpx;font-size: 80%;color:#fe3b0f ;"
              @click.stop>
          全部
        </view>
      </view>
    </view>
    <view class="feedback-type list-input">
      <view class="title">
        <text>手续费</text>
      </view>
      <view class="content">
        {{ percent }}%
      </view>
    </view>
    <block v-if="selectToIndex == 3">
      <view class="feedback-type" style="background-color: #f6f6f6;">
        <view>
          <view>目标用户账户信息</view>
          <text style="font-size: 85%;color:#fe3b0f ;">转账前请确认好互转账户，一旦转错，资产将无法找回。</text>
        </view>
      </view>
      <view class="feedback-type" @click="inputToUsername">
        <view class="title">
          <text>实名姓名</text>
        </view>
        <view class="content">
          {{ toUsername }}
          <text class="iconfont icon-more"></text>
        </view>
      </view>
      <view class="feedback-type" @click="inputToMobile">
        <view class="title">
          <text>手机号</text>
        </view>
        <view class="content">
          {{ toMobile }}
          <text class="iconfont icon-more"></text>
        </view>
      </view>
      <view class="feedback-type">
        <view class="title">
          <text>匹配信息</text>
        </view>
        <view class="content">
          <text v-if="same===true" style="color: seagreen;">
            <text class="iconfont icon-success"></text>
            {{ sameStr }}
          </text>
          <text v-if="same===false" style="color: #fe3b0f;">
            <text class="iconfont icon-close1"></text>
            {{ sameStr }}
          </text>
        </view>
      </view>
    </block>
    <view class="feedback-type list-input" style="height: 200rpx;" v-if="selectToIndex==2">
      <view class="title">
        <text>银行账户</text>
      </view>
      <view class="content" style="width: 70%;">
        <view>户名：{{ bank.card_name }}</view>
        <view>身份证：{{ bank.carID }}</view>
        <view>银行：{{ bank.card_bank }}</view>
        <view>账户：{{ bank.card_id }}</view>
      </view>
    </view>
    <view class="feedback-type list-input" style="height: 200rpx;" v-if="selectToIndex==2">
      <view class="title">
        <text>提现说明</text>
      </view>
      <view class="content" style="width: 70%;font-weight: bold;height: 150rpx;overflow-y: auto;"
            v-html="'每个账户每天仅限一次提现操作。<br>参拍货款或拍卖增值可以二选一。<br>提现到账时间严格按照T+1/T+3/T+5执行，请大家熟悉提现规则。<br>规范提现是我们发展的重要基础，感谢大家的理解与支持!'">

      </view>
    </view>
    <!-- 提交 -->
    <view class="submit-btn" @click="postFeedBack">
      <text>提交</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      TypeArray: ['商品相关', '物流状况', '客户服务', '优惠活动', '产品体验', '产品功能', '其他问题'],
      TypeIndex: 0,
      content: '',
      bank: {},
      mobile: '',
      selectIndex: 0,
      numbers: 0,//兑换数量
      percent: 5,
      percent2: 5,
      selectToIndex: '',
      amount: 0,
      toUsername: '',
      toMobile: '',
      same: '',
      sameStr: '',
      toUserID: 0,
      _radioReset: Date.now() // 用于强制更新radio组件的状态
    };
  },
  onLoad() {
    this.$login.checkLogin({login: true})
    uni.setNavigationBarColor({
      frontColor: '#ffffff',
      backgroundColor: '#fe3b0f',
    })
    this.getUserAmount(0)
  },
  methods: {
    calcPercent() {
      if (this.selectIndex == 0) {
        if (this.selectToIndex == 1) {
          this.percent = 0
        }
        if (this.selectToIndex == 2) {
          this.percent = 0
        }
        if (this.selectToIndex == 3) {
          this.percent = 0
        }
      }
      if (this.selectIndex == 1) {
        if (this.selectToIndex == 1) {
          this.percent = this.percent2
        }
        if (this.selectToIndex == 2) {
          this.percent = this.percent2
        }
        if (this.selectToIndex == 3) {
          this.percent = 0
        }
      }
      if (this.selectIndex == 2) {
        if (this.selectToIndex == 1) {
          this.percent = 0
        }
        if (this.selectToIndex == 2) {
          this.percent = 0
        }
        if (this.selectToIndex == 3) {
          this.percent = 0
        }
      }
    },
    selectTo(value) {
      this.selectToIndex = value.detail.value
      this.calcPercent()
    },
    getUserAmount(cint) {
      // 先保存当前状态
      const oldSelectIndex = this.selectIndex

      //如果重复点击同一个tab，则不做任何操作
      if (oldSelectIndex === cint) {
        return
      }
      // 更新选中的标签
      this.selectIndex = cint

      // 清除radio选中状态
      this.selectToIndex = ''

      // 使用uni-app的方式重置radio组件状态
      // 创建一个临时变量，用于在模板中强制更新
      this._radioReset = Date.now()

      // 使用setTimeout确保在下一个事件循环中执行，让视图有时间更新
      setTimeout(() => {
        // 如果需要，可以在这里添加额外的逻辑
        // 例如通过uni.createSelectorQuery()查找并操作元素
        // 但在大多数情况下，设置selectToIndex为空字符串应该足够了
      }, 50)

      var that = this
      that.$http.post('getUserAmount', {
        token: getApp().globalData.token,
        selectIndex: that.selectIndex + 1,
      }).then(res => {
        if (res.code == 0) {
          that.amount = res.data.amount
          that.bank = res.data.bank
          that.percent2 = res.data.percent
          that.calcPercent()
        } else {
          uni.showToast({
            title: res.msg
          })
        }
      })
    },
    checkToUser() {
      if (this.toUsername == '' || this.toMobile == '') {
        return false
      }
      uni.showLoading({
        title: "检测中..."
      })
      this.same = ''
      var that = this
      that.$http.post('checkToUser', {
        token: getApp().globalData.token,
        toUsername: that.toUsername,
        toMobile: that.toMobile,
      }).then(res => {
        setTimeout(() => {
          uni.hideLoading()
          that.sameStr = res.msg
          if (res.code == 0) {
            that.same = true
            that.toUserID = res.data.userID
          } else {
            that.same = false
          }
        }, 1000)

      })
    },
    inputToUsername() {
      uni.showModal({
        editable: true,
        title: "请输入目标账户实名姓名：",
        success: (re) => {
          if (re == '') {
            uni.showToast({
              title: "输入错误"
            })
            return false
          }
          if (re.confirm) {
            this.toUsername = re.content
            this.checkToUser()
          }
        }
      })
    },
    inputToMobile() {
      uni.showModal({
        editable: true,
        title: "请输入目标账户手机号：",
        success: (re) => {
          if (re == '') {
            uni.showToast({
              title: "输入错误"
            })
            return false
          }
          if (re.confirm) {
            this.toMobile = re.content
            this.checkToUser()
          }
        }
      })
    },
    inputNumber() {
      uni.showModal({
        editable: true,
        title: "请输入数量：",
        placeholderText: "最低提现金额为500",
        success: (re) => {
          if (re.confirm) {
            const inputValue = re.content.trim()

            // 检查是否为空
            if (!inputValue) {
              uni.showToast({
                title: "请输入数量",
                icon: "none"
              })
              return false
            }

            // 检查是否为数字
            if (!/^\d+(\.\d+)?$/.test(inputValue)) {
              uni.showToast({
                title: "请输入有效的数字",
                icon: "none"
              })
              return false
            }

            const number = parseFloat(inputValue)

            // 检查是否小于500
            if (number < 500) {
              uni.showToast({
                title: "不能少于500",
                icon: "none"
              })
              return false
            }

            // 检查是否大于可用余额
            if (number > this.amount) {
              uni.showToast({
                title: "不能大于可用余额" + this.amount,
                icon: "none"
              })
              return false
            }

            // 验证通过，设置数量
            this.numbers = number
          }
        }
      })
    },
    postFeedBack() {
      var that = this
      if (this.selectToIndex == 2) {
        if (this.bank.card_bank == null || this.bank.card_id == null || this.bank.card_bank == '' || this.bank.card_id == '') {
          uni.showToast({
            title: "先绑定银行卡"
          })
          return false
        }
      }
      uni.showModal({
        title: '是否确定提交',
        success: function (re) {
          if (re.confirm) {
            that.$http.post('amountChange', {
              token: getApp().globalData.token,
              selectIndex: that.selectIndex,
              selectToIndex: that.selectToIndex,
              toUserID: that.toUserID,
              numbers: that.numbers
            }).then(res => {
              uni.stopPullDownRefresh();
              if (res.code == 0) {
                uni.showToast({
                  title: "提交成功"
                })
                setTimeout(function () {
                  uni.navigateBack()
                }, 1500)
              } else {
                uni.showToast({
                  title: res.msg
                })
              }
            })
          }
        }
      })
    },
    /**
     * 反馈类型
     * @param {Object} val
     */
    FeedbackTypeCh(val) {

      this.TypeIndex = val.detail.value;

    }
  }
}
</script>

<style scoped lang="scss">
@import 'amount.scss';
</style>
