<script>
	const apiUrl = process.env.NODE_ENV === 'development' ? 'https://devapi.rongyipai.cn' : 'https://api.rongyipai.cn';

	export default {
		globalData: {
			token: '',
			apiUrl: apiUrl,
			selectAddress: {},
			config: {},
			level: 0,
      //是不是手动检查版本更新
      isManualCheckUpdate: false,
		},
		onLaunch: function() {

			console.log('App Launch');
			// #ifdef  APP-PLUS
			// console.log('App Launch');

			uni.request({
				url:getApp().globalData.apiUrl+"api/ads",
				success: (res) => {
					console.log(res.data)
				    if( res.data.code == 0){
				     	//app启动时打开启动广告页
				     	var w = plus.webview.open(
				     		getApp().globalData.apiUrl+'/api/ad', // 远程链接或者本地链接
				     		'advertise', {
				     			top: 0,
				     			bottom: 0,
				     			zindex: 9999
				     		},
				     		'fade-in',
				     		0    // 不为0的时候会有tabbar先出现的问题
				     	);
				     	//设置定时器，4s后关闭启动广告页
				     	setTimeout(function() {
				     		plus.webview.close(w);
				     	}, 6000);
				     	uni.setStorageSync('x_close',false)
				     	var getwebview = setInterval(function(){
				     		var x_close=w.getStyle()
				     		if(x_close == null){
				     			console.log('已关闭')
				     			uni.setStorageSync('x_close',true)
				     			clearInterval(getwebview)
				     		}
				     	},100)
				    }
				}
			})

			//#endif
		},
		onShow: function() {
			console.log('App Show');
			// setTimeout(()=>{
			//   uni.hideTabBar()
			// },100)
		},
		onHide: function() {
			console.log('App Hide');
		},
		methods: {
			setMenuTop() {
				var that = this
				uni.getSystemInfo({
					success: function(info) {
						// 状态栏的高度
						var windowTop = info.windowTop;
						getApp().globalData.windowTop = windowTop + "px"
						var windowBottom = info.windowBottom;
						getApp().globalData.windowBottom = windowBottom + "px"
					}
				});
			},
			getSystemInfo() {
				const systemInfo = uni.getSystemInfoSync();
				if (systemInfo.platform === 'android') {
					// this.isAndroid = true;
					// this.isIOS = false;
					return "android";
				} else if (systemInfo.platform === 'ios') {
					// this.isAndroid = false;
					// this.isIOS = true;
					return 'ios'
				}
				return 'other'
			},
			// 检查更新方法，可供不同地方调用
			checkUpdate(isManualCheckUpdate) {
        this.isManualCheckUpdate = isManualCheckUpdate;
				// #ifndef APP
				return false
				// #endif
				var sys = this.getSystemInfo()
				if (sys != 'android' && sys != 'ios') {
					return false;
				}

				let that = this
				uni.getSystemInfo({
					success: (res) => {
						console.log(res.platform);
						//检测当前平台，如果是安卓则启动安卓更新
						if (res.platform == "android") {
							that.AndroidCheckUpdate('android')
						}
						if (res.platform == "ios") {
							that.AndroidCheckUpdate('ios')
						}
					}
				})
				return true;
			},

			// 原有的appUp方法，调用checkUpdate
			appUp() {
				return this.checkUpdate();
			},
			AndroidCheckUpdate(os) {
				// #ifndef  APP-PLUS
				return false;
				// #endif
				let that = this;
				plus.runtime.getProperty(plus.runtime.appid, (wgtinfo) => {
					that.version = wgtinfo.version //客户端版本号
					that.version_num = wgtinfo.versionCode //客户端版本号
					console.log('当前app版本信息：' + that.version);
					console.log('版本号:', wgtinfo.versionCode);
					//设置默认
					var downURL = 'iosversion';
					if (os == 'android') {
						downURL = 'androidversion';
					}
					console.log(downURL)
					uni.request({
						url: getApp().globalData.apiUrl + "/api/"+downURL+"?token=" + getApp().globalData.token + "&fromID="+getApp().globalData.fromID,
						success(res) {
							console.log(res)
							if (res.data.code == 0) {
								if(res.data.data.length < 1){
									return false
								}
								if (wgtinfo.versionCode < res.data.data.num) {
									if (res.data.data.must == 1) {
										uni.showModal({
											title: '版本更新',
											confirmText: '立即更新',
											showCancel: false,
											content: res.data.data.content,
											success() {
												console.log('confirm')
												uni.showToast({
													title: '已转到后台更新'
												})
												setTimeout(()=>{
													uni.showLoading({
														title:'下载中 勿退出'
													})
												},1000)
												if (os == 'android') {
													that.downANDinstall(res.data.data.url)
												} else {
													plus.runtime.openURL(res.data.data.url)
												}
											}
										})
									} else {
										var upmust = uni.getStorageSync('upmust');
										var version = uni.getStorageSync('version');
										console.log('本地版本：', version)
										console.log('服务器版本：', res.data.data.num)
										if (upmust != 0 || version < res.data.data.num) {
											uni.showModal({
												showCancel: true,
												cancelText: '下次',
												confirmText: '立即更新',
												title: '版本更新',
												content: res.data.data.content,
												success(ress) {
													if (ress.confirm) {
														console.log('confirm')
														uni.showToast({
															title: '已转到后台更新'
														})
														if (os == 'android') {
															var re = that.downANDinstall(res.data
																.data.url)
															if (re == true) {
																uni.setStorage({
																	key: 'version',
																	data: res.data.data.num
																})
															}
														} else {
															plus.runtime.openURL(res.data.data.url)
														}

													} else {
														console.log('saveupdate')
														uni.setStorage({
															key: 'upmust',
															data: 0
														})
														uni.setStorage({
															key: 'version',
															data: res.data.data.num
														})
													}
												}
											})
										}

									}
								}else {
                  if (that.isManualCheckUpdate){
                    uni.showToast({
                      title: '当前为最新版本'
                    })
                  }
                }
							} else {
								// uni.$u.toast(res.msg);
								uni.showModal({
									title: '提示',
									content: res.msg,
									showCancel: false
								})
							}
						}
					})
				})
				// that.getUpdateVersion()
			},
			downANDinstall(downloadApkUrl) {
				console.log(downloadApkUrl)
				var dtask = plus.downloader.createDownload(downloadApkUrl, {}, function(d, status) {
					// 下载完成
					console.log(status)
					if (status == 200) {
						plus.runtime.install(plus.io.convertLocalFileSystemURL(d.filename), {}, {}, function(
						error) {
							uni.showToast({
								title: '安装成功',
								duration: 1500
							});
							return false
						})
					} else {
						uni.showToast({
							title: '更新失败',
							duration: 1500
						});
						return false
					}
				});
				dtask.start();
				return true;
			},
		}
	};
</script>

<style lang="scss">
	/*每个页面公共css */
	@import 'colorui/main.css';
	@import 'colorui/icon.css';
	@import 'style/FontStyle.css';
	body {
		-webkit-text-size-adjust: none;
		font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
	}
	.s-title-w{font-size: 36rpx;color: #fff;}
	.s-btn-s{background-color: $base;height: 40rpx;border-radius: 20rpx;color: $uni-bg-color;text-align: center;font-size: 90%;line-height: 40rpx;padding: 0 20rpx;}
	.s-flex-column{display: flex;flex-direction: column;}
	.s-flex{display: flex;}
	.s-flex-justcon-center{justify-content: center;}
	.s-width25-1{width: calc(25% - 1px);}
	.s-width25-2{width: calc(25% - 2px) !important;}
	.s-width25{width: 25% !important;}
	.s-flex-bt{justify-content: space-between;}
	.s-flex-justitems-center{justify-items: center;}
	.s-paiBG{width: 100%;border-radius: 20rpx 20rpx 0 0;background-color: $base;bottom: 0;color: #fff;text-align: center;padding: 10rpx 0;}
	.s-paiBG > view{width: 50%;}
	.s-sort-boder {position: absolute;top: 15rpx;bottom: 15rpx;left: 0;width: 1px;background-color: #fff;}
	.s-pos-rel{position: relative;}
	.s-pos-abs{position: absolute;}
	.s-linear-gradient-1{background: linear-gradient(to bottom, #005cb9, #0080ff);}
	.s-linear-gradient-2{background: linear-gradient(to bottom, #ff557f, #ff72ce);}
	.s-linear-gradient-3{background: linear-gradient(to bottom, #73ac00, #7ebd00);}
	/* 超出一行省略号 */
	.one-omit {
		white-space: nowrap;
		/*规定段落中的文本不进行换行*/
		overflow: hidden;
		/*内容会被修剪，并且其余内容是不可见的。*/
		text-overflow: ellipsis;
		/*显示省略号来代表被修剪的文本*/
	}

	.two-omit {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		overflow: hidden;
		text-overflow: ellipsis;
		-webkit-box-orient: vertical;
	}

	img {
		width: 100%;
	}

	.on-anything {
		text-align: center;
		padding: 100rpx 0;
		color: #ccc;
		width: 100%;
	}

	.flex {
		display: flex;
	}

	.loginContract checkbox {
		transform: scale(0.8);
	}
	.flexItemCenter {
		align-items: center;
	}

</style>
