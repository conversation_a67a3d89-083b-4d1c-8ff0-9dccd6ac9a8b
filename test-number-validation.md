# 数字输入验证功能测试说明

## 功能概述
为 `pages/ConfirmAuctionOrder/ConfirmAuctionOrder.vue` 页面的数量输入框添加了完整的输入验证功能。

## 实现的功能

### 1. 输入监听
- 在输入框上添加了 `@input="onNumberInput"` 事件监听
- 每次用户输入都会触发验证

### 2. 持续监听
- 使用 Vue 的 `watch` 监听器监听 `number` 变量的变化
- 确保无论通过何种方式修改数量都会被验证

### 3. 验证规则
- 只允许输入正整数（大于0的整数）
- 自动清除所有非数字字符
- 如果输入为空或0，自动设置为1
- 如果输入包含非法字符，显示提示并清除

### 4. 错误提示
- 输入非法字符时：显示"已自动清除非法字符"
- 输入空值或0时：显示"请输入大于0的整数"
- 其他无效输入：显示"数量必须是大于0的整数"

## 测试场景

### 场景1：输入非数字字符
- 输入：`12a3`
- 结果：自动清除为 `123`，显示"已自动清除非法字符"

### 场景2：输入特殊字符
- 输入：`12.5`
- 结果：自动清除为 `125`，显示"已自动清除非法字符"

### 场景3：输入空值
- 输入：``（空）
- 结果：自动设置为 `1`，显示"请输入大于0的整数"

### 场景4：输入0
- 输入：`0`
- 结果：自动设置为 `1`，显示"请输入大于0的整数"

### 场景5：输入负数符号
- 输入：`-5`
- 结果：自动清除为 `5`，显示"已自动清除非法字符"

### 场景6：正常输入
- 输入：`10`
- 结果：正常显示 `10`，无错误提示

## 代码修改点

1. **模板修改**：在输入框添加 `@input="onNumberInput"` 事件
2. **添加 watch 监听器**：监听 number 变量变化
3. **新增方法**：
   - `onNumberInput()`: 处理输入事件
   - `validateAndCleanNumber()`: 验证并清理输入
   - `validateNumber()`: 验证数字值
   - `showNumberError()`: 显示错误提示
4. **修改现有方法**：
   - `jiajia()`: 确保加减操作也遵循验证规则
   - `calcCanBuy()`: 确保计算结果至少为1

## 兼容性
- 适用于 UniApp 的所有平台（H5、小程序、App）
- 使用原生 Vue 特性，无需额外依赖
